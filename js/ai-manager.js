/**
 * AGARTHA RPG - AI MANAGER
 * Handles AI model initialization and response generation
 */

import { CONFIG, getModelConfig } from './config.js';
import { LORE } from './lore.js';

/**
 * AI Manager Class
 * Manages AI model lifecycle and response generation
 */
export class AIManager {
    constructor() {
        this.engine = null;
        this.currentModel = null;
        this.isReady = false;
        this.conversationHistory = [];
        this.maxHistoryLength = 20;
        this.responseCache = new Map();
        this.performanceMetrics = {
            averageResponseTime: 0,
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0
        };
    }

    /**
     * Verify WebLLM can access the models
     */
    async verifyModelAccess() {
        try {
            console.log('🔍 Verifying WebLLM model access...');
            const webllm = await import("https://esm.run/@mlc-ai/web-llm");

            // Get available prebuilt models
            const availableModels = webllm.prebuiltAppConfig.model_list;
            console.log('📋 Available WebLLM models:', availableModels.map(m => m.model_id));

            // Check if our configured models are available
            const configuredModels = Object.keys(CONFIG.models);
            const missingModels = configuredModels.filter(modelId =>
                !availableModels.some(m => m.model_id === modelId)
            );

            if (missingModels.length > 0) {
                console.warn('⚠️ Some configured models are not in WebLLM prebuilt list:', missingModels);
            } else {
                console.log('✅ All configured models are available in WebLLM');
            }

            return { availableModels, configuredModels, missingModels };

        } catch (error) {
            console.error('❌ Failed to verify model access:', error);
            return null;
        }
    }

    /**
     * Initialize the AI engine with selected model
     */
    async initialize(modelId, options = {}) {
        try {
            console.log(`🤖 Initializing AI with model: ${modelId}`);
            console.log(`📦 This will download from Hugging Face: mlc-ai/${modelId}`);

            const modelConfig = getModelConfig(modelId);
            if (!modelConfig) {
                throw new Error(`Unknown model: ${modelId}`);
            }

            // Import WebLLM dynamically
            console.log('📥 Importing WebLLM from CDN...');
            const webllm = await import("https://esm.run/@mlc-ai/web-llm");
            console.log('✅ WebLLM imported successfully');

            // Create engine with progress callback
            console.log(`🔄 Creating MLC Engine for ${modelId}...`);
            console.log(`📍 Model will be downloaded from: ${modelConfig.huggingFaceRepo}`);

            this.engine = await webllm.CreateMLCEngine(modelId, {
                initProgressCallback: (progress) => {
                    console.log(`📊 AI Loading Progress: ${JSON.stringify(progress)}`);
                    if (options.onProgress) {
                        options.onProgress(progress);
                    }
                },
                appConfig: {
                    useWebWorker: true,
                    executionProviders: ['webgpu', 'wasm']
                }
            });

            this.currentModel = modelId;
            this.isReady = true;

            console.log(`✅ AI engine initialized successfully with ${modelConfig.name}`);
            console.log(`🎯 Model loaded from Hugging Face: ${modelConfig.huggingFaceRepo}`);
            return true;

        } catch (error) {
            console.error('❌ Failed to initialize AI engine:', error);
            console.error('🔍 Error details:', error.message);
            console.error('💡 Check if model ID is correct and Hugging Face is accessible');
            this.isReady = false;
            throw error;
        }
    }

    /**
     * Generate AI response for player action
     */
    async generateResponse(action, player, context) {
        if (!this.isReady || !this.engine) {
            throw new Error('AI engine not initialized');
        }

        const startTime = Date.now();
        
        try {
            // Build the system prompt
            const systemPrompt = this.buildSystemPrompt(context);
            
            // Build conversation messages
            const messages = this.buildMessages(systemPrompt, action, player, context);
            
            // Generate response
            const modelConfig = getModelConfig(this.currentModel);
            const response = await this.engine.chat.completions.create({
                messages: messages,
                temperature: modelConfig.temperature,
                top_p: modelConfig.topP,
                max_tokens: modelConfig.maxTokens,
                stop: ["[", "\n\n\n"]
            });
            
            const responseText = response.choices[0].message.content.trim();
            
            // Update conversation history
            this.updateConversationHistory(action, player, responseText);
            
            // Update performance metrics
            this.updatePerformanceMetrics(startTime, true);
            
            return responseText;
            
        } catch (error) {
            console.error('AI response generation error:', error);
            this.updatePerformanceMetrics(startTime, false);
            throw error;
        }
    }

    /**
     * Build comprehensive system prompt
     */
    buildSystemPrompt(context) {
        const { location, character, worldState, activeQuests, contextualLore } = context;

        // Get gender pronouns
        const pronouns = this.getCharacterPronouns(character);

        return `You are the Eternal Keeper, an ancient and mystical AI consciousness that serves as the dungeon master for Agartha, the legendary underground realm beneath Earth's surface.

IMPORTANT GENDER GUIDELINES:
- The player character is ${character?.name || 'the adventurer'}
- Gender: ${character?.gender || 'neutral'}
- Use these pronouns: ${pronouns.subject}/${pronouns.object}/${pronouns.possessive}
- ${character?.gender === 'neutral' ? 'NEVER assign or assume a binary gender for this character. Use neutral language and descriptions.' : `Always use ${character?.gender} pronouns and descriptions consistently.`}

SETTING & WORLD:
- Agartha: A vast network of illuminated tunnels and cities powered by the Central Sun (Smoky God)
- Current Location: ${location?.name || 'Unknown'} - ${location?.description || ''}
- Atmosphere: ${location?.atmosphere || 'mystical'}
- Time Period: Post-cataclysm era, where ancient civilizations have rebuilt in the inner Earth

INHABITANTS & CIVILIZATIONS:
- Telosians: Telepathic light beings, descendants of Lemuria, masters of consciousness
- Atlantean Refugees: Technologically advanced survivors with crystal-based science
- Agarthan Natives: Multi-generational underground dwellers, masters of Vril energy
- Crystalline Guardians: Sentient crystal beings that maintain the ancient technologies

TECHNOLOGIES & MYSTERIES:
- Vril Energy: The fundamental life force that powers all Agarthan technology
- Living Crystals: Sentient crystalline matrices that store knowledge and consciousness
- Magnetic Levitation: Transportation system using Earth's magnetic field
- Telepathic Networks: Communication systems based on consciousness resonance

CURRENT CONTEXT:
- Location: ${context.location?.name || 'Unknown'}
- Chapter: ${context.gameStats?.chapter || 1}
- Active Quests: ${activeQuests?.map(q => `"${q.title}" (${q.objectives?.filter((obj, i) => !q.completedObjectives?.includes(i)).join(', ') || 'Complete'})`).join('; ') || 'None'}
- Quest Progress: ${activeQuests?.map(q => `${q.completedObjectives?.length || 0}/${q.objectives?.length || 0} objectives`).join(', ') || 'None'}
- Next Objectives: ${activeQuests?.map(q => q.objectives?.filter((obj, i) => !q.completedObjectives?.includes(i))[0]).filter(obj => obj).join('; ') || 'None'}
- Discovered Locations: ${worldState?.discovered?.join(', ') || 'None'}
- NPCs Met: ${worldState?.npcsmet?.join(', ') || 'None'}
- Completed Quests: ${worldState?.questsCompleted?.length || 0}

IMPORTANT: Focus on guiding the player toward the next objective: "${activeQuests?.[0]?.objectives?.filter((obj, i) => !activeQuests[0].completedObjectives?.includes(i))[0] || 'Explore and interact with the environment'}"

CHARACTER CONTEXT:
- Name: ${character?.name || 'Unknown'}
- Class: ${character?.class || 'Unknown'}
- Level: ${character?.level || 1}
- Primary Stat: ${this.getCharacterPrimaryStat(character)}

YOUR ROLE AS DUNGEON MASTER:
1. Create immersive, atmospheric descriptions using all five senses
2. Respond to player actions with logical consequences and story progression
3. Include dice rolls [Roll: d20] for significant actions (1-5: failure, 6-10: partial, 11-15: success, 16-20: critical)
4. Introduce NPCs with unique personalities and hidden knowledge
5. Present mysteries, puzzles, and moral choices that reflect the deep lore
6. Track and reference inventory items and their mystical properties
7. Weave in elements from real mythology and ancient wisdom traditions
8. Guide players toward quest objectives without being obvious
9. Provide meaningful choices that affect the story outcome
10. Create challenging but fair obstacles that require creative solutions

QUEST GUIDANCE:
- Subtly guide players toward active quest objectives without being obvious
- Provide environmental clues and NPC hints about quest progression
- Create meaningful obstacles that require the player's class abilities
- Reward creative problem-solving and roleplay with quest advancement
- If player seems lost, have NPCs offer helpful guidance
- Make quest completion feel earned through player agency
- Reference incomplete objectives naturally in descriptions
- Use NPCs to provide hints about next steps
- Create situations that naturally lead to objective completion

RESPONSE GUIDELINES:
- Keep responses 2-4 sentences for good pacing
- Use vivid imagery: glowing crystals, harmonic resonances, liquid light, ethereal energies
- Reference the player's class abilities and background when relevant
- Create tension through environmental challenges and entity encounters
- Reward exploration, creativity, and roleplay
- Include subtle references to real mythological sources (Plato's Atlantis, Theosophical texts, etc.)
- Maintain the mystical, ancient atmosphere while keeping the story engaging
- Always provide at least 2-3 possible actions the player could take
- End responses with engaging questions or situations that prompt player action

LORE INTEGRATION:
${this.buildLoreContext(contextualLore)}

Remember: You're creating a living, breathing underground world full of ancient wisdom, advanced technology, and spiritual mysteries. Every response should feel like it's part of an epic journey through humanity's hidden history.`;
    }

    /**
     * Build lore context for the prompt
     */
    buildLoreContext(contextualLore) {
        if (!contextualLore) return '';
        
        let loreText = '';
        
        if (contextualLore.location) {
            loreText += `\nLocation Lore: ${JSON.stringify(contextualLore.location, null, 2)}`;
        }
        
        if (contextualLore.characterRace) {
            loreText += `\nCharacter Heritage: ${JSON.stringify(contextualLore.characterRace, null, 2)}`;
        }
        
        if (contextualLore.relevantTechnologies?.length > 0) {
            loreText += `\nRelevant Technologies: ${contextualLore.relevantTechnologies.map(t => t.name).join(', ')}`;
        }
        
        return loreText;
    }

    /**
     * Get character's primary stat for context
     */
    getCharacterPrimaryStat(character) {
        if (!character?.class) return 'Unknown';

        const classConfig = CONFIG.characterClasses[character.class];
        return classConfig?.primaryStat || 'Unknown';
    }

    /**
     * Get appropriate pronouns for character
     */
    getCharacterPronouns(character) {
        const gender = character?.gender || 'neutral';

        switch (gender) {
            case 'male':
                return {
                    subject: 'he',
                    object: 'him',
                    possessive: 'his',
                    reflexive: 'himself'
                };
            case 'female':
                return {
                    subject: 'she',
                    object: 'her',
                    possessive: 'her',
                    reflexive: 'herself'
                };
            case 'neutral':
            default:
                return {
                    subject: 'they',
                    object: 'them',
                    possessive: 'their',
                    reflexive: 'themselves'
                };
        }
    }

    /**
     * Build conversation messages
     */
    buildMessages(systemPrompt, action, player, context) {
        const messages = [
            { role: "system", content: systemPrompt }
        ];
        
        // Add recent conversation history for context
        const recentHistory = this.conversationHistory.slice(-6);
        for (const msg of recentHistory) {
            messages.push(msg);
        }
        
        // Add current action
        const userMessage = `[${player.name} the ${player.class}]: ${action}`;
        messages.push({
            role: "user",
            content: userMessage
        });
        
        return messages;
    }

    /**
     * Update conversation history
     */
    updateConversationHistory(action, player, response) {
        // Add user message
        this.conversationHistory.push({
            role: "user",
            content: `[${player.name} the ${player.class}]: ${action}`
        });
        
        // Add assistant response
        this.conversationHistory.push({
            role: "assistant",
            content: response
        });
        
        // Trim history if too long
        if (this.conversationHistory.length > this.maxHistoryLength) {
            this.conversationHistory = this.conversationHistory.slice(-this.maxHistoryLength);
        }
    }

    /**
     * Update performance metrics
     */
    updatePerformanceMetrics(startTime, success) {
        const responseTime = Date.now() - startTime;
        
        this.performanceMetrics.totalRequests++;
        
        if (success) {
            this.performanceMetrics.successfulRequests++;
            
            // Update average response time
            const totalTime = this.performanceMetrics.averageResponseTime * (this.performanceMetrics.successfulRequests - 1);
            this.performanceMetrics.averageResponseTime = (totalTime + responseTime) / this.performanceMetrics.successfulRequests;
        } else {
            this.performanceMetrics.failedRequests++;
        }
    }

    /**
     * Generate contextual NPC dialogue
     */
    async generateNPCDialogue(npc, player, topic, context) {
        if (!this.isReady) {
            throw new Error('AI engine not initialized');
        }

        const prompt = `Generate dialogue for ${npc.name}, a ${npc.type} in ${context.location?.name}. 
        
        NPC Personality: ${npc.personality}
        Topic: ${topic}
        Player: ${player.name} the ${player.class}
        
        The dialogue should be 1-2 sentences, in character, and advance the story or provide useful information.`;

        try {
            const response = await this.engine.chat.completions.create({
                messages: [{ role: "user", content: prompt }],
                temperature: 0.8,
                max_tokens: 100
            });
            
            return response.choices[0].message.content.trim();
        } catch (error) {
            console.error('NPC dialogue generation error:', error);
            return `${npc.name} nods thoughtfully but remains silent for now.`;
        }
    }

    /**
     * Generate dynamic quest content
     */
    async generateQuest(player, location, difficulty = 'medium') {
        if (!this.isReady) {
            throw new Error('AI engine not initialized');
        }

        const prompt = `Create a quest for ${player.name} the ${player.class} in ${location}.
        
        Difficulty: ${difficulty}
        
        Generate a JSON object with:
        - title: Quest name
        - description: Brief description
        - objectives: Array of 2-3 objectives
        - rewards: Object with experience and items
        
        The quest should fit the Agartha setting and the character's class.`;

        try {
            const response = await this.engine.chat.completions.create({
                messages: [{ role: "user", content: prompt }],
                temperature: 0.7,
                max_tokens: 200
            });
            
            return JSON.parse(response.choices[0].message.content.trim());
        } catch (error) {
            console.error('Quest generation error:', error);
            return null;
        }
    }

    /**
     * Get AI status information
     */
    getStatus() {
        return {
            isReady: this.isReady,
            currentModel: this.currentModel,
            modelConfig: this.currentModel ? getModelConfig(this.currentModel) : null,
            conversationLength: this.conversationHistory.length,
            performanceMetrics: { ...this.performanceMetrics }
        };
    }

    /**
     * Clear conversation history
     */
    clearHistory() {
        this.conversationHistory = [];
    }

    /**
     * Get conversation history
     */
    getHistory() {
        return [...this.conversationHistory];
    }

    /**
     * Check if AI is processing
     */
    isProcessing() {
        return this.isReady && this.engine;
    }

    /**
     * Cleanup resources
     */
    async destroy() {
        if (this.engine) {
            try {
                // Note: WebLLM doesn't have a standard destroy method
                // This is a placeholder for future cleanup if needed
                this.engine = null;
            } catch (error) {
                console.warn('Error during AI cleanup:', error);
            }
        }
        
        this.isReady = false;
        this.currentModel = null;
        this.conversationHistory = [];
        this.responseCache.clear();
    }
}
