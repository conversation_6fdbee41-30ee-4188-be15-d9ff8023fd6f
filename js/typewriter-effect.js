/**
 * Typewriter Effect Utility for Agartha RPG
 * Provides smooth typewriter animation for AI responses
 */

class TypewriterEffect {
    constructor() {
        this.activeAnimations = new Map();
        this.defaultSpeed = 30; // milliseconds per character
        this.punctuationDelay = 150; // extra delay for punctuation
    }

    /**
     * Create a typewriter effect for text content
     * @param {HTMLElement} container - The container element
     * @param {string} text - The text to animate
     * @param {Object} options - Animation options
     * @returns {Promise} - Resolves when animation completes
     */
    async typeText(container, text, options = {}) {
        const {
            speed = this.defaultSpeed,
            showCursor = true,
            onComplete = null,
            onCharacter = null,
            preserveHTML = true
        } = options;

        // Clear any existing animation
        this.stopAnimation(container);

        // Create typewriter container
        const typewriterContainer = document.createElement('div');
        typewriterContainer.className = 'typewriter-container';
        
        const textElement = document.createElement('span');
        textElement.className = 'typewriter-text';
        
        const cursorElement = document.createElement('span');
        cursorElement.className = 'typewriter-cursor';
        
        typewriterContainer.appendChild(textElement);
        if (showCursor) {
            typewriterContainer.appendChild(cursorElement);
        }

        // Clear container and add typewriter elements
        container.innerHTML = '';
        container.appendChild(typewriterContainer);

        // Handle HTML content vs plain text
        let characters;
        if (preserveHTML && this.containsHTML(text)) {
            characters = this.parseHTMLForTyping(text);
        } else {
            characters = text.split('');
        }

        // Create animation promise
        const animationPromise = new Promise((resolve) => {
            let currentIndex = 0;
            let currentText = '';

            const typeNextCharacter = () => {
                if (currentIndex >= characters.length) {
                    // Animation complete
                    if (showCursor) {
                        cursorElement.remove();
                    }
                    this.activeAnimations.delete(container);
                    if (onComplete) onComplete();
                    resolve();
                    return;
                }

                const char = characters[currentIndex];
                currentText += char;
                
                if (preserveHTML && this.containsHTML(text)) {
                    textElement.innerHTML = currentText;
                } else {
                    textElement.textContent = currentText;
                }

                if (onCharacter) onCharacter(char, currentIndex);

                currentIndex++;

                // Calculate delay based on character type
                let delay = speed;
                if (this.isPunctuation(char)) {
                    delay += this.punctuationDelay;
                }

                const timeoutId = setTimeout(typeNextCharacter, delay);
                this.activeAnimations.set(container, { timeoutId, resolve });
            };

            typeNextCharacter();
        });

        return animationPromise;
    }

    /**
     * Create an enhanced thinking animation
     * @param {HTMLElement} container - The container element
     * @param {string} message - Optional custom thinking message
     * @returns {HTMLElement} - The thinking element for removal
     */
    createThinkingAnimation(container, message = 'The Eternal Keeper is thinking') {
        const thinkingElement = document.createElement('div');
        thinkingElement.className = 'message ai-message ai-thinking-enhanced';
        thinkingElement.innerHTML = `
            <div class="message-content">
                <span class="thinking-dots">${message}</span>
            </div>
        `;
        
        container.appendChild(thinkingElement);
        container.scrollTop = container.scrollHeight;
        
        return thinkingElement;
    }

    /**
     * Stop any active animation for a container
     * @param {HTMLElement} container - The container element
     */
    stopAnimation(container) {
        const animation = this.activeAnimations.get(container);
        if (animation) {
            clearTimeout(animation.timeoutId);
            animation.resolve();
            this.activeAnimations.delete(container);
        }
    }

    /**
     * Check if text contains HTML tags
     * @param {string} text - Text to check
     * @returns {boolean}
     */
    containsHTML(text) {
        return /<[^>]*>/g.test(text);
    }

    /**
     * Parse HTML content for character-by-character typing
     * @param {string} html - HTML content
     * @returns {Array} - Array of characters and HTML tags
     */
    parseHTMLForTyping(html) {
        const result = [];
        let inTag = false;
        let currentTag = '';
        
        for (let i = 0; i < html.length; i++) {
            const char = html[i];
            
            if (char === '<') {
                inTag = true;
                currentTag = char;
            } else if (char === '>' && inTag) {
                currentTag += char;
                result.push(currentTag);
                currentTag = '';
                inTag = false;
            } else if (inTag) {
                currentTag += char;
            } else {
                result.push(char);
            }
        }
        
        return result;
    }

    /**
     * Check if character is punctuation
     * @param {string} char - Character to check
     * @returns {boolean}
     */
    isPunctuation(char) {
        return /[.!?,:;]/.test(char);
    }

    /**
     * Add typewriter effect to AI message
     * @param {HTMLElement} messageElement - The message element
     * @param {string} content - The content to type
     * @param {Object} options - Animation options
     * @returns {Promise}
     */
    async animateAIMessage(messageElement, content, options = {}) {
        const contentElement = messageElement.querySelector('.message-content');
        if (!contentElement) {
            console.warn('No message-content element found for typewriter effect');
            contentElement.innerHTML = content;
            return;
        }

        // Add typewriter styling
        messageElement.classList.add('typewriter-message');
        
        return this.typeText(contentElement, content, {
            speed: 25, // Slightly faster for better UX
            showCursor: true,
            preserveHTML: true,
            ...options
        });
    }

    /**
     * Stop all active animations
     */
    stopAllAnimations() {
        for (const [container] of this.activeAnimations) {
            this.stopAnimation(container);
        }
    }
}

// Create global instance
window.typewriterEffect = new TypewriterEffect();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TypewriterEffect;
}
