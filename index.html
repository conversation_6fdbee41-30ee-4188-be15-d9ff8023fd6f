<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agartha: The Lost City of Light - BETA - AI-Powered RPG</title>
    <meta name="description" content="BETA VERSION - Explore the mystical underground realm of Agartha with an AI dungeon master powered by Web-LLM - No login required">
    <meta name="keywords" content="Agartha, RPG, AI, Lemuria, Atlantis, underground, mystical, adventure">
    <meta name="author" content="Agartha Development Team">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔮</text></svg>">



    <!-- CSS Stylesheets -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/game.css">
    <link rel="stylesheet" href="styles/multiplayer.css">

    <!-- Character creation animations -->
    <style>
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .character-creation.active {
            animation: fadeIn 0.5s ease;
        }



            .stat-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 6px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner animate-spin"></div>
            <div class="loading-text">Channeling the Akashic Records...</div>
        </div>
    </div>

    <!-- Model Setup Screen -->
    <div class="model-setup" id="modelSetup">
        <div class="card glass-strong slide-in-up">
            <div class="card-header text-center">
                <h1 class="animate-glow">🔮 Agartha: The Lost City of Light</h1>
                <p class="card-subtitle">AI-Powered RPG Adventure - BETA VERSION</p>
                <div class="badge badge-warning mb-md">🚧 Open Beta - No Login Required</div>
            </div>

            <div class="panel mb-lg">
                <div class="panel-header">
                    <h3 class="text-primary">✨ Choose Your AI Oracle</h3>
                </div>
                <p class="text-medium">Select an AI model to power your dungeon master. <strong>Demo Mode is recommended</strong> for instant testing and guaranteed compatibility.</p>

                <div class="mt-md">
                    <div class="badge badge-success mb-sm">🚀 No API Keys Required</div>
                    <div class="badge badge-secondary mb-sm">🔒 Runs Locally</div>
                    <div class="badge badge-warning mb-sm">⚡ WebGPU Accelerated</div>
                    <div class="badge badge-info mb-sm">🎭 Demo Mode Available</div>
                    <div class="badge badge-primary mb-sm">🐭 Super Tiny Models</div>
                </div>

                <div class="mt-md p-md" style="background: rgba(0, 255, 255, 0.1); border: 1px solid rgba(0, 255, 255, 0.3); border-radius: 8px;">
                    <p style="color: #00ffff; font-size: 0.85rem; margin: 0;">
                        <strong>🎭 Recommended:</strong> Start with Demo Mode for instant access! AI models require large downloads and may not be available in all WebLLM versions.
                    </p>
                </div>
            </div>

            <div class="model-options" id="modelOptions">
                <!-- Model options will be populated by JavaScript -->
            </div>

            <div class="panel" id="modelStatus">
                <div class="flex flex-center">
                    <div class="status-indicator" id="statusIndicator"></div>
                    <div id="statusText" class="text-medium">Select a model to begin your journey...</div>
                </div>
                <div class="progress-bar mt-md" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill" style="width: 0%">0%</div>
                </div>
            </div>

            <div class="text-center mt-lg">
                <button class="btn btn-primary" id="startBtn" disabled>
                    <span>🧙‍♂️ Initialize AI Dungeon Master</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Character Creation Screen -->
    <div class="character-creation hidden" id="characterCreation">
        <div class="character-creation-container">
            <div class="character-creation-header">
                <h1 class="creation-title animate-glow">🌟 Create Your Avatar</h1>
                <p class="creation-subtitle">The Lost City of Light Awaits Your Arrival...</p>
                <div class="creation-badge">🎮 Beta Access - Jump Right In!</div>
            </div>

            <div class="character-creation-content">
                <div class="character-form-section">
                    <!-- Character Name -->
                    <div class="form-section">
                        <label for="playerName" class="section-label">Character Name</label>
                        <input type="text" id="playerName" class="character-input"
                               placeholder="Enter your character's name" maxlength="30" required>
                        <small class="input-hint">Choose a name that resonates with ancient power</small>
                    </div>

                    <!-- Gender Selection -->
                    <div class="form-section">
                        <label class="section-label">Character Identity</label>
                        <div class="gender-selection">
                            <div class="gender-option" data-gender="male">
                                <div class="gender-icon">♂️</div>
                                <span class="gender-label">Male</span>
                            </div>
                            <div class="gender-option" data-gender="female">
                                <div class="gender-icon">♀️</div>
                                <span class="gender-label">Female</span>
                            </div>
                            <div class="gender-option" data-gender="neutral">
                                <div class="gender-icon">⚧</div>
                                <span class="gender-label">Neutral</span>
                            </div>
                        </div>
                        <small class="input-hint">This helps the AI use appropriate pronouns and descriptions</small>
                    </div>

                    <!-- Character Classes -->
                    <div class="form-section">
                        <label class="section-label">Choose Your Sacred Path</label>
                        <div id="characterClasses" class="character-classes-grid">
                            <!-- Character classes will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Character Background -->
                    <div class="form-section">
                        <label for="characterDesc" class="section-label">Character Background</label>
                        <textarea id="characterDesc" class="character-textarea" rows="4"
                                  placeholder="Describe your character's appearance, personality, and backstory..."
                                  maxlength="300"></textarea>
                        <div class="textarea-footer">
                            <small class="input-hint">This will help the AI create more personalized adventures</small>
                            <span class="character-count" id="characterCount">0/300</span>
                        </div>
                    </div>
                </div>

                <!-- Character Preview -->
                <div class="character-preview-section" id="characterPreview" style="display: none;">
                    <h3 class="preview-title">Character Preview</h3>
                    <div class="preview-content" id="previewContent">
                        <!-- Character preview will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Action Button -->
            <div class="character-creation-footer">
                <button class="create-character-btn" id="createCharacterBtn" disabled>
                    <span class="btn-icon">🚪</span>
                    <span class="btn-text">Enter the Inner Earth</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Game Container -->
    <div class="main-container" id="gameContainer">
        <!-- Left Sidebar -->
        <aside class="sidebar">
            <!-- Character Stats Panel -->
            <div class="panel">
                <h2>⚡ Your Essence</h2>
                <div id="characterInfo" class="character-stats">
                    <!-- Character info will be populated by JavaScript -->
                </div>
            </div>

            <!-- Inventory Panel -->
            <div class="panel">
                <h2>🎒 Sacred Artifacts</h2>
                <div class="inventory-grid" id="inventory">
                    <!-- Inventory slots will be populated by JavaScript -->
                </div>
            </div>

            <!-- Active Quests Panel -->
            <div class="panel">
                <h2>📜 Sacred Quests</h2>
                <div class="quest-list" id="questList">
                    <!-- Quest list will be populated by JavaScript -->
                </div>
            </div>

            <!-- Fellow Seekers Panel -->
            <div class="panel">
                <h2>🌟 Fellow Seekers</h2>
                <div class="player-list" id="playerList">
                    <!-- Player list will be populated by JavaScript -->
                </div>
            </div>

            <!-- AI Oracle Panel -->
            <div class="panel">
                <h2>🤖 AI Oracle</h2>
                <div class="ai-status">
                    <div class="ai-indicator loading" id="aiIndicator"></div>
                    <div class="ai-info">
                        <div class="ai-model" id="aiModelName">Initializing...</div>
                        <div class="ai-stats" id="aiStats">Preparing neural pathways...</div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Chat Area -->
        <main class="chat-container">
            <header class="chat-header">
                <h1>The Chronicles of Agartha</h1>
                <div class="scenario-info">
                    <span class="scenario-location" id="currentLocation">Crystal Gates of Shambhala</span>
                </div>
            </header>

            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message fade-in">
                    <div class="message-content glass p-lg text-center">
                        <h2 class="text-primary mb-md">🔮 Welcome to Agartha</h2>
                        <p class="text-medium">The ancient realm beneath the Earth awaits your exploration. Your AI Dungeon Master will guide you through this mystical journey.</p>
                        <div class="mt-md">
                            <div class="badge badge-success">✨ AI-Powered Storytelling</div>
                            <div class="badge badge-warning">🎲 Dynamic Adventures</div>
                            <div class="badge badge-secondary">🌍 Rich Lore</div>
                        </div>
                    </div>
                </div>
            </div>

            <footer class="chat-input">
                <div class="quick-actions">
                    <button class="quick-action" data-action="look around">👁️ Look</button>
                    <button class="quick-action" data-action="examine surroundings">🔍 Examine</button>
                    <button class="quick-action" data-action="meditate on the energy">🧘 Meditate</button>
                    <button class="quick-action" data-action="channel vril energy">✨ Use Vril</button>
                    <button class="quick-action" data-action="reach out telepathically">🧠 Telepathy</button>
                    <button class="quick-action" data-action="search for hidden passages">🚪 Search</button>
                </div>

                <div class="input-container">
                    <input type="text" id="messageInput"
                           placeholder="Describe your action in the mystical realm..."
                           maxlength="500" autocomplete="off">
                    <button id="sendBtn">Send</button>
                </div>
            </footer>
        </main>
    </div>

    <!-- Modals and Overlays -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal glass-strong" id="modal">
            <div class="modal-header">
                <h3 id="modalTitle">Modal Title</h3>
                <button class="modal-close" id="modalClose">✕</button>
            </div>
            <div class="modal-content" id="modalContent">
                <!-- Modal content will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Inline Styles and Scripts -->
    <style>
        /* CSS Variables */
        :root {
            --bg-dark: #0a0a0a;
            --bg-card: rgba(255, 255, 255, 0.05);
            --primary-cyan: #00ffff;
            --primary-purple: #8a2be2;
            --success-green: #00ff88;
            --warning-gold: #ffd700;
            --error-red: #ff4444;
            --text-light: #ffffff;
            --text-medium: #cccccc;
            --text-dim: #888888;
            --border-glass: 1px solid rgba(255, 255, 255, 0.2);
            --border-radius: 12px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --transition-fast: 0.2s ease;
            --transition-medium: 0.3s ease;
            --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.3);
            --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        }

        /* Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: radial-gradient(ellipse at center, rgba(138, 43, 226, 0.3) 0%, var(--bg-dark) 70%);
            color: var(--text-light);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Model Setup Screen */
        .model-setup {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .model-setup.hidden {
            display: none;
        }

        .card {
            background: var(--gradient-card);
            border: var(--border-glass);
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-medium);
            max-width: 600px;
            width: 90%;
        }

        .card-header {
            text-align: center;
            margin-bottom: var(--spacing-lg);
        }

        .card-header h1 {
            color: var(--primary-cyan);
            font-size: 2.5em;
            margin-bottom: var(--spacing-sm);
            text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .card-subtitle {
            color: var(--text-dim);
            font-style: italic;
        }

        .panel {
            background: var(--bg-card);
            border: var(--border-glass);
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .panel h3 {
            color: var(--primary-cyan);
            margin-bottom: var(--spacing-sm);
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            margin-right: var(--spacing-sm);
        }

        .badge-success { background: rgba(0, 255, 136, 0.2); color: var(--success-green); }
        .badge-secondary { background: rgba(255, 255, 255, 0.1); color: var(--text-medium); }
        .badge-warning { background: rgba(255, 215, 0, 0.2); color: var(--warning-gold); }

        .model-options {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .model-option {
            background: var(--bg-card);
            border: var(--border-glass);
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            cursor: pointer;
            transition: var(--transition-medium);
        }

        .model-option:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary-cyan);
        }

        .model-option.selected {
            border-color: var(--primary-cyan);
            background: rgba(0, 255, 255, 0.1);
        }

        .model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .model-name {
            color: var(--primary-cyan);
            margin: 0;
        }

        .model-badges {
            display: flex;
            gap: var(--spacing-sm);
        }

        .model-description {
            color: var(--text-medium);
            margin-bottom: var(--spacing-sm);
        }

        .model-specs {
            display: flex;
            gap: var(--spacing-md);
            font-size: 0.9em;
            color: var(--text-dim);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--text-dim);
            margin-right: var(--spacing-sm);
        }

        .status-indicator.online {
            background: var(--success-green);
            box-shadow: 0 0 10px var(--success-green);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin-top: var(--spacing-sm);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-cyan), var(--primary-purple));
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #000;
            font-weight: bold;
        }

        /* Login Screen */
        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at center, rgba(138, 43, 226, 0.2) 0%, rgba(0, 0, 0, 0.9) 100%);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .login-screen.active {
            display: flex;
        }

        .login-card {
            background: var(--gradient-card);
            border: var(--border-glass);
            border-radius: 20px;
            padding: 40px;
            width: 90%;
            max-width: 500px;
            box-shadow: var(--shadow-medium);
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--primary-cyan);
            font-weight: 500;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: #ffffff;
            font-size: 16px;
            transition: var(--transition-medium);
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-cyan);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .form-select option {
            background: var(--bg-dark);
        }

        .class-description {
            background: var(--bg-card);
            border: var(--border-glass);
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            margin-top: var(--spacing-sm);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, var(--primary-cyan) 0%, #0088cc 100%);
            border: none;
            border-radius: 8px;
            color: #000;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: var(--transition-medium);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.4);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Main Game Container */
        .main-container {
            display: none;
            flex: 1;
            padding: 20px;
            max-width: 1600px;
            width: 100%;
            margin: 0 auto;
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .main-container.active {
            display: flex;
        }

        .sidebar {
            width: 320px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .panel h2 {
            color: var(--primary-cyan);
            margin-bottom: 15px;
            font-size: 1.2em;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .character-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
            transition: var(--transition-medium);
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.08);
        }

        .stat-label {
            color: var(--text-dim);
            font-size: 0.9em;
        }

        .stat-value {
            color: var(--primary-cyan);
            font-weight: bold;
        }

        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            margin-top: 15px;
        }

        .inventory-slot {
            aspect-ratio: 1;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            cursor: pointer;
            transition: var(--transition-medium);
        }

        .inventory-slot:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary-cyan);
        }

        .inventory-slot.filled {
            background: rgba(0, 255, 255, 0.1);
            border-color: var(--primary-cyan);
        }

        .player-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
        }

        .player-item {
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid var(--primary-cyan);
            transition: var(--transition-medium);
        }

        .player-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateX(3px);
        }

        .player-item.current-player {
            border-left-color: var(--success-green);
            background: rgba(0, 255, 0, 0.05);
        }

        .player-name {
            color: var(--primary-cyan);
            font-weight: bold;
            margin-bottom: 5px;
        }

        .player-class {
            color: var(--text-dim);
            font-size: 0.9em;
        }

        .player-status {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-top: 5px;
            font-size: 0.85em;
        }

        .ai-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin-top: 10px;
        }

        .ai-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .ai-indicator.active {
            background: var(--success-green);
            box-shadow: 0 0 10px var(--success-green);
        }

        .ai-indicator.loading {
            background: var(--warning-gold);
            box-shadow: 0 0 10px var(--warning-gold);
        }

        .ai-indicator.error {
            background: var(--error-red);
            box-shadow: 0 0 10px var(--error-red);
        }

        .ai-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .ai-model {
            color: var(--primary-cyan);
            font-weight: bold;
            font-size: 0.9em;
        }

        .ai-stats {
            color: var(--text-dim);
            font-size: 0.8em;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--gradient-card);
            border: var(--border-glass);
            border-radius: 15px;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .chat-header {
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header h1 {
            color: var(--primary-cyan);
            font-size: 1.5em;
            text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
        }

        .scenario-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            font-size: 0.9em;
        }

        .scenario-location {
            color: var(--warning-gold);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            padding: 15px;
            border-radius: 10px;
            max-width: 85%;
            position: relative;
        }

        .message.dm {
            background: linear-gradient(135deg, rgba(138, 43, 226, 0.2) 0%, rgba(138, 43, 226, 0.1) 100%);
            border: 1px solid rgba(138, 43, 226, 0.3);
            align-self: flex-start;
            border-top-left-radius: 0;
        }

        .message.player {
            background: linear-gradient(135deg, rgba(0, 191, 255, 0.2) 0%, rgba(0, 191, 255, 0.1) 100%);
            border: 1px solid rgba(0, 191, 255, 0.3);
            align-self: flex-end;
            border-top-right-radius: 0;
        }

        .message.system {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%);
            border: 1px solid rgba(255, 215, 0, 0.3);
            align-self: center;
            text-align: center;
            max-width: 60%;
        }

        .message-author {
            font-weight: bold;
            margin-bottom: 8px;
            color: var(--primary-cyan);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .message-timestamp {
            font-size: 0.75em;
            color: var(--text-dim);
            font-weight: normal;
        }

        .message-content {
            color: var(--text-light);
            line-height: 1.6;
        }

        .message-content .roll {
            display: inline-block;
            padding: 2px 8px;
            background: rgba(255, 215, 0, 0.2);
            border: 1px solid var(--warning-gold);
            border-radius: 4px;
            margin: 0 4px;
            font-weight: bold;
            color: var(--warning-gold);
        }

        .message-content .location {
            color: var(--primary-cyan);
            font-weight: bold;
        }

        .message-content .npc {
            color: #ff69b4;
            font-style: italic;
        }

        .message-content .item {
            color: var(--success-green);
            font-weight: bold;
        }

        .chat-input {
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .quick-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }

        .quick-action {
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: var(--text-dim);
            font-size: 0.85em;
            cursor: pointer;
            transition: var(--transition-medium);
        }

        .quick-action:hover {
            background: rgba(0, 255, 255, 0.1);
            border-color: var(--primary-cyan);
            color: var(--primary-cyan);
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: #ffffff;
            font-size: 16px;
        }

        .chat-input input:focus {
            outline: none;
            border-color: var(--primary-cyan);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .chat-input button {
            padding: 12px 30px;
            background: linear-gradient(135deg, var(--primary-cyan) 0%, #0088cc 100%);
            border: none;
            border-radius: 8px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
            transition: var(--transition-medium);
        }

        .chat-input button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0, 255, 255, 0.4);
        }

        .chat-input button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-cyan);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                flex-direction: row;
                overflow-x: auto;
                gap: 15px;
            }

            .panel {
                min-width: 280px;
            }
        }

        @media (max-width: 768px) {
            .card {
                padding: 20px;
            }

            .chat-header {
                flex-direction: column;
                gap: 10px;
            }

            .message {
                max-width: 95%;
            }

            .quick-actions {
                justify-content: center;
            }
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 10px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(0, 255, 255, 0.3);
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 255, 255, 0.5);
        }
    </style>

    <!-- Test if JavaScript is working -->
    <script>
        console.log('🔧 Basic JavaScript test - this should appear in console');

        // Test if DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 DOM loaded');

            // Test if we can find the model container
            const container = document.getElementById('modelOptions');
            console.log('🔧 Model container found:', container ? 'YES' : 'NO');

            if (container) {
                // Add a simple test message
                container.innerHTML = '<div style="color: white; padding: 20px; border: 2px solid red;">JavaScript is working! Models should load here...</div>';
            }
        });
    </script>

    <!-- Typewriter Effect -->
    <script src="js/typewriter-effect.js"></script>

    <!-- Bundled JavaScript for GoDaddy hosting compatibility -->
    <script src="js/agartha-bundle.js"></script>
</body>
</html>
