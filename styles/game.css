/* ===== AGARTHA RPG - GAME SPECIFIC STYLES ===== */

/* ===== LAYOUT CONTAINERS ===== */

/* ===== MOBILE-FIRST RESPONSIVE LAYOUT ===== */

.model-setup,
.character-creation {
    min-height: 100vh;
    background: radial-gradient(ellipse at center, rgba(138, 43, 226, 0.2) 0%, rgba(0, 0, 0, 0.9) 100%);
    padding: 10px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.model-setup .card,
.character-creation .card {
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 15px;
    border-radius: 15px;
}

/* Tablet and up */
@media (min-width: 768px) {
    .model-setup,
    .character-creation {
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        padding-top: 40px;
    }

    .model-setup .card,
    .character-creation .card {
        max-width: 700px;
        padding: 30px;
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .model-setup .card,
    .character-creation .card {
        max-width: 800px;
        padding: 40px;
    }
}

.model-setup.hidden,
.character-creation.hidden {
    display: none;
}

.character-creation.active {
    display: flex;
}

.game-container {
    display: none;
    min-height: 100vh;
    background: var(--gradient-background);
}

.game-container.active {
    display: flex;
}

.game-sidebar {
    width: 320px;
    min-width: 280px;
    max-width: 400px;
    background: rgba(0, 0, 0, 0.3);
    border-right: var(--border-glass);
    backdrop-filter: blur(10px);
    overflow-y: auto;
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    position: relative;
}

/* ===== MODEL SELECTION ===== */

.model-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin: 15px 0;
}

/* Mobile model options */
@media (max-width: 767px) {
    .model-options {
        gap: 10px;
        margin: 10px 0;
    }
}

.model-option {
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    touch-action: manipulation;
}

/* Mobile model options */
@media (max-width: 767px) {
    .model-option {
        padding: 12px;
        border-radius: 10px;
    }

    .model-option:hover {
        transform: none; /* Disable hover transform on mobile */
    }

    .model-option:active {
        transform: scale(0.98);
        background: rgba(0, 255, 255, 0.15);
    }
}

.model-option:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: var(--primary-cyan);
    transform: translateX(5px);
}

.model-option.selected {
    background: rgba(0, 255, 255, 0.2);
    border-color: var(--primary-cyan);
    box-shadow: var(--shadow-glow);
}

.model-option.selected::after {
    content: '✓';
    position: absolute;
    right: 15px;
    top: 15px;
    color: var(--success-green);
    font-size: 1.5rem;
    font-weight: bold;
}

/* Model card content */
.model-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.model-header h3 {
    color: var(--primary-cyan);
    margin: 0;
    font-size: 1.1rem;
    flex: 1;
    min-width: 0;
}

.model-specs {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

.model-description {
    color: var(--text-medium);
    font-size: 0.9rem;
    margin: 8px 0;
    line-height: 1.4;
}

.model-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-top: 10px;
    font-size: 0.85rem;
}

.model-stats .stat {
    display: flex;
    justify-content: space-between;
    color: var(--text-dim);
}

.stat-label {
    color: var(--text-medium);
}

.stat-value {
    color: var(--primary-cyan);
    font-weight: 600;
}

/* Mobile model card adjustments */
@media (max-width: 767px) {
    .model-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .model-header h3 {
        font-size: 1rem;
    }

    .model-description {
        font-size: 0.85rem;
    }

    .model-stats {
        grid-template-columns: 1fr;
        gap: 6px;
        font-size: 0.8rem;
    }

    .model-option.selected::after {
        right: 12px;
        top: 12px;
        font-size: 1.2rem;
    }
}

.model-option h4 {
    color: var(--primary-cyan);
    margin-bottom: var(--spacing-sm);
    font-size: 1.1rem;
}

.model-option p {
    color: var(--text-dim);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-sm);
}

.model-specs {
    display: flex;
    gap: var(--spacing-md);
    font-size: 0.85rem;
    color: var(--warning-amber);
    flex-wrap: wrap;
}

/* ===== CHARACTER CREATION REDESIGN ===== */

.character-creation-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background: var(--gradient-card);
    border: var(--border-glass);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    box-shadow: var(--shadow-strong);
}

.character-creation-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.creation-title {
    font-size: 2.5rem;
    color: var(--primary-cyan);
    margin-bottom: 10px;
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.creation-subtitle {
    font-size: 1.1rem;
    color: var(--text-dim);
    font-style: italic;
    margin-bottom: 15px;
}

.creation-badge {
    display: inline-block;
    background: var(--success-green);
    color: var(--bg-dark);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.character-creation-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

.character-form-section {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-section {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    transition: all 0.3s ease;
}

.form-section:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 255, 255, 0.3);
}

.section-label {
    display: block;
    color: var(--primary-cyan);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 12px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.character-input {
    width: 100%;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.character-input:focus {
    outline: none;
    border-color: var(--primary-cyan);
    background: rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.2);
}

.character-textarea {
    width: 100%;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
    transition: all 0.3s ease;
}

.character-textarea:focus {
    outline: none;
    border-color: var(--primary-cyan);
    background: rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.2);
}

.textarea-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
}

.input-hint {
    color: var(--text-dim);
    font-size: 0.9rem;
    font-style: italic;
}

.character-count {
    color: var(--text-dim);
    font-size: 0.85rem;
    font-weight: 500;
}

/* Gender Selection */
.gender-selection {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 10px;
}

.gender-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 15px;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.gender-option:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.5);
    transform: translateY(-2px);
}

.gender-option.selected {
    background: rgba(0, 255, 255, 0.2);
    border-color: var(--primary-cyan);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.gender-icon {
    font-size: 2rem;
    margin-bottom: 8px;
}

.gender-label {
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Character Classes Grid */
.character-classes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    margin-bottom: 10px;
}

.character-class-card {
    background: rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.character-class-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.character-class-card:hover::before {
    left: 100%;
}

.character-class-card:hover {
    background: rgba(0, 0, 0, 0.6);
    border-color: rgba(0, 255, 255, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.character-class-card.selected {
    background: rgba(0, 255, 255, 0.15);
    border-color: var(--primary-cyan);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
}

.class-header {
    margin-bottom: 15px;
}

.class-name {
    color: var(--primary-cyan);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 5px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.class-title {
    color: var(--text-dim);
    font-size: 0.9rem;
    font-style: italic;
    margin-bottom: 10px;
}

.class-description {
    color: var(--text-light);
    font-size: 0.95rem;
    line-height: 1.4;
    margin-bottom: 15px;
}

.class-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 0.85rem;
}

.stat-name {
    color: var(--text-dim);
    text-transform: capitalize;
}

.stat-value {
    color: var(--primary-cyan);
    font-weight: 600;
}

/* Character Preview Section */
.character-preview-section {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    animation: slideInUp 0.5s ease;
}

.preview-title {
    color: var(--primary-cyan);
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.preview-content {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
}

.preview-character-info {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-name {
    color: var(--primary-cyan);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.preview-class {
    color: var(--text-light);
    font-size: 1rem;
    margin-bottom: 5px;
}

.preview-gender {
    color: var(--text-dim);
    font-size: 0.9rem;
    font-style: italic;
}

.preview-description {
    color: var(--text-light);
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 20px;
    text-align: center;
}

.preview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
}

.preview-stat {
    text-align: center;
    padding: 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-stat-label {
    display: block;
    color: var(--text-dim);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.preview-stat-value {
    color: var(--primary-cyan);
    font-size: 1.2rem;
    font-weight: 700;
}

/* Character Creation Footer */
.character-creation-footer {
    margin-top: 30px;
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.create-character-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 18px 40px;
    background: linear-gradient(135deg, var(--success-green), #00cc88);
    color: white;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 204, 136, 0.3);
    position: relative;
    overflow: hidden;
}

.create-character-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.create-character-btn:hover::before {
    left: 100%;
}

.create-character-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 204, 136, 0.4);
}

.create-character-btn:active {
    transform: translateY(-1px);
}

.create-character-btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-dim);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.create-character-btn:disabled::before {
    display: none;
}

.btn-icon {
    font-size: 1.3rem;
}

.btn-text {
    font-weight: 700;
}

/* ===== RESPONSIVE CHARACTER CREATION ===== */

/* Tablet */
@media (min-width: 768px) {
    .character-creation-content {
        grid-template-columns: 2fr 1fr;
        gap: 40px;
    }

    .creation-title {
        font-size: 3rem;
    }

    .character-classes-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .gender-selection {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .character-creation-container {
        max-width: 1200px;
        padding: 40px;
    }

    .character-classes-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Mobile */
@media (max-width: 767px) {
    .character-creation {
        padding: 10px;
    }

    .character-creation-container {
        padding: 15px;
        border-radius: 15px;
    }

    .creation-title {
        font-size: 2rem;
    }

    .creation-subtitle {
        font-size: 1rem;
    }

    .form-section {
        padding: 15px;
    }

    .character-input,
    .character-textarea {
        padding: 12px 15px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .gender-selection {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .gender-option {
        flex-direction: row;
        justify-content: flex-start;
        padding: 15px;
        gap: 15px;
    }

    .gender-icon {
        font-size: 1.5rem;
        margin-bottom: 0;
    }

    .character-classes-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .character-class-card {
        padding: 15px;
    }

    .class-name {
        font-size: 1.1rem;
    }

    .class-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }

    .stat-item {
        padding: 5px 8px;
        font-size: 0.8rem;
    }

    .create-character-btn {
        padding: 15px 30px;
        font-size: 1rem;
        width: 100%;
        max-width: 300px;
    }

    .preview-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .preview-stat {
        padding: 8px;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .character-creation-container {
        padding: 10px;
        margin: 5px;
    }

    .creation-title {
        font-size: 1.8rem;
    }

    .form-section {
        padding: 12px;
    }

    .character-class-card {
        padding: 12px;
    }

    .class-stats-grid {
        grid-template-columns: 1fr;
    }
}

.preview-stat {
    text-align: center;
    padding: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius);
}

.preview-stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-dim);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.preview-stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-cyan);
    margin-top: var(--spacing-xs);
}

/* ===== CHAT INTERFACE ===== */

.chat-header {
    padding: var(--spacing-lg);
    border-bottom: var(--border-glass);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.header-content {
    flex: 1;
    min-width: 0;
}

.game-title {
    font-size: 1.8rem;
    margin-bottom: var(--spacing-sm);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.location-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.location-icon {
    font-size: 1.2rem;
}

.location-name {
    font-weight: 600;
    color: var(--primary-cyan);
    font-size: 1.1rem;
}

.location-details {
    display: flex;
    gap: var(--spacing-sm);
    margin-left: var(--spacing-sm);
}

.game-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.message {
    max-width: 85%;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-large);
    backdrop-filter: blur(5px);
    position: relative;
    animation: slideInUp 0.3s ease-out;
}

.message.player {
    align-self: flex-end;
    background: var(--gradient-primary);
    color: white;
    border-bottom-right-radius: var(--spacing-sm);
}

.message.dm {
    align-self: flex-start;
    background: rgba(255, 255, 255, 0.1);
    border: var(--border-glass);
    border-bottom-left-radius: var(--spacing-sm);
}

.message.system {
    align-self: center;
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid rgba(255, 215, 0, 0.3);
    text-align: center;
    font-style: italic;
    max-width: 70%;
}

.message-author {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-timestamp {
    font-size: 0.75rem;
    opacity: 0.7;
    font-weight: normal;
}

.message-content {
    line-height: 1.6;
}

.welcome-message {
    margin: var(--spacing-xl) auto;
    max-width: 600px;
}

/* ===== CHAT INPUT ===== */

.chat-input {
    padding: var(--spacing-lg);
    border-top: var(--border-glass);
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.2);
}

.quick-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
    justify-content: center;
}

.quick-action {
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: var(--border-glass);
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.quick-action:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: var(--primary-cyan);
    transform: translateY(-2px);
}

.input-container {
    position: relative;
}

.input-wrapper {
    display: flex;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

.input-wrapper .form-input {
    flex: 1;
    min-height: 48px;
    resize: none;
    padding-right: 120px;
}

.input-actions {
    display: flex;
    gap: var(--spacing-xs);
    position: absolute;
    right: var(--spacing-sm);
    bottom: var(--spacing-sm);
}

.input-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
    font-size: 0.8rem;
    color: var(--text-dim);
}

.character-count {
    opacity: 0.7;
}

.typing-indicator {
    color: var(--primary-cyan);
    font-style: italic;
}

/* ===== CHARACTER STATS ===== */

.character-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.character-header {
    text-align: center;
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.character-name {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-cyan);
    margin-bottom: var(--spacing-xs);
}

.character-class {
    font-size: 0.9rem;
    color: var(--text-dim);
    font-style: italic;
}

.character-level {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    font-size: 0.85rem;
}

.health-bar,
.vril-bar {
    margin-bottom: var(--spacing-md);
}

.bar-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
    font-size: 0.9rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-dim);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-weight: bold;
    color: var(--primary-cyan);
}

/* ===== INVENTORY ===== */

.inventory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
}

.inventory-slot {
    aspect-ratio: 1;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.inventory-slot:hover {
    border-color: var(--primary-cyan);
    background: rgba(0, 255, 255, 0.1);
}

.inventory-slot.filled {
    background: rgba(0, 255, 255, 0.2);
    border-color: var(--primary-cyan);
    animation: crystalGlow 3s ease-in-out infinite;
}

.inventory-slot.empty {
    color: var(--text-dim);
    font-size: 1rem;
}

/* ===== QUEST LIST ===== */

.quest-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 300px;
    overflow-y: auto;
}

.quest-item {
    background: rgba(255, 255, 255, 0.05);
    border: var(--border-glass);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.quest-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-cyan);
}

.quest-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quest-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.quest-description {
    font-size: 0.85rem;
    line-height: 1.4;
    margin: 0;
}

.quest-progress {
    margin: var(--spacing-sm) 0;
}

.quest-objectives {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.objective {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.85rem;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.objective.completed {
    background: rgba(0, 255, 136, 0.1);
    border-left: 2px solid var(--success-green);
}

.objective.active {
    background: rgba(255, 255, 255, 0.05);
    border-left: 2px solid var(--warning-amber);
}

.objective-icon {
    font-size: 0.9rem;
    flex-shrink: 0;
}

.objective-text {
    flex: 1;
    line-height: 1.3;
}

.objective-text.text-success {
    text-decoration: line-through;
    opacity: 0.8;
}

/* ===== PLAYER LIST ===== */

.player-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.player-item {
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: var(--border-glass);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.player-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.player-item.current-player {
    border-color: var(--primary-cyan);
    background: rgba(0, 255, 255, 0.1);
}

.player-name {
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
}

.player-class {
    font-size: 0.8rem;
    color: var(--text-dim);
    margin-bottom: var(--spacing-xs);
}

.player-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
}

/* ===== MODAL ===== */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 4000;
    backdrop-filter: blur(5px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    max-width: 90vw;
    max-height: 90vh;
    width: 600px;
    overflow: hidden;
    animation: scaleIn 0.3s ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: var(--border-glass);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--error-red);
}

.modal-content {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: 70vh;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
    .game-sidebar {
        width: 280px;
    }
    
    .chat-header {
        padding: var(--spacing-md);
    }
    
    .game-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .game-container {
        flex-direction: column;
    }
    
    .game-sidebar {
        width: 100%;
        height: auto;
        max-height: 40vh;
        border-right: none;
        border-bottom: var(--border-glass);
        flex-direction: row;
        overflow-x: auto;
        padding: var(--spacing-sm);
    }
    
    .game-sidebar .panel {
        min-width: 250px;
        flex-shrink: 0;
    }
    
    .chat-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .location-info {
        width: 100%;
        justify-content: space-between;
    }
    
    .quick-actions {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: var(--spacing-sm);
    }
    
    .message {
        max-width: 95%;
    }
}

@media (max-width: 480px) {
    .model-setup,
    .character-creation {
        padding: var(--spacing-md);
    }
    
    .card {
        padding: var(--spacing-lg);
    }
    
    .chat-messages {
        padding: var(--spacing-md);
    }
    
    .chat-input {
        padding: var(--spacing-md);
    }
    
    .input-wrapper {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .input-actions {
        position: static;
        justify-content: flex-end;
    }
    
    .input-wrapper .form-input {
        padding-right: var(--spacing-md);
    }
}
